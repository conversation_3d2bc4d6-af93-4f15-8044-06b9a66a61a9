{"ast": null, "code": "var _jsxFileName = \"D:\\\\predixo\\\\predixo_webtech\\\\predixo-react\\\\src\\\\components\\\\game_development\\\\GameDevStats.js\";\nimport React from 'react';\nimport './GameDevStats.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst stats = [{\n  value: 120,\n  suffix: '',\n  color: '#FB5343',\n  title: 'Games Built',\n  subtitle: 'Across platforms',\n  percent: 100\n}, {\n  value: 85,\n  suffix: '',\n  color: '#E33FA1',\n  title: 'NFT Integration',\n  subtitle: 'Blockchain based games',\n  percent: 85\n}, {\n  value: 75,\n  suffix: '',\n  color: '#6549D5',\n  title: 'Multiplayer',\n  subtitle: 'Online real-time games',\n  percent: 75\n}, {\n  value: 65,\n  suffix: '',\n  color: '#36C6F0',\n  title: 'Metaverse Ready',\n  subtitle: 'Immersive 3D games',\n  percent: 65\n}];\nconst GameDevStats = () => /*#__PURE__*/_jsxDEV(\"section\", {\n  className: \"rainbow-progressbar-area rainbow-section-gap game-dev-stats-section\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-lg-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-title text-center\",\n          \"data-sal\": \"slide-up\",\n          \"data-sal-duration\": \"700\",\n          \"data-sal-delay\": \"100\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"subtitle text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"theme-gradient\",\n              children: \"Gaming Development Stats\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"title w-600 mb--20\",\n            children: \"Our Game Development Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row row--30\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-lg-10 offset-lg-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row mt_dec--30\",\n          children: stats.map((stat, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-3 col-md-6 col-12 col-sm-6 mt--30\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"radial-progress-single\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"radial-progress\",\n                style: {\n                  '--percent': stat.percent,\n                  '--bar-color': stat.color,\n                  '--track-color': '#0f0f11'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"circle-text\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"count\",\n                    children: [stat.value, stat.suffix]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 64,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"progress-ring\",\n                  width: \"225\",\n                  height: \"225\",\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    className: \"progress-ring__track\",\n                    cx: \"112.5\",\n                    cy: \"112.5\",\n                    r: \"100\",\n                    stroke: \"#0f0f11\",\n                    strokeWidth: \"10\",\n                    fill: \"none\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 67,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    className: \"progress-ring__bar\",\n                    cx: \"112.5\",\n                    cy: \"112.5\",\n                    r: \"100\",\n                    stroke: stat.color,\n                    strokeWidth: \"10\",\n                    fill: \"none\",\n                    strokeDasharray: 2 * Math.PI * 100,\n                    strokeDashoffset: 2 * Math.PI * 100 * (1 - stat.percent / 100),\n                    style: {\n                      transition: 'stroke-dashoffset 1.2s cubic-bezier(0.4,0,0.2,1)'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 68,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"circle-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"title\",\n                  children: stat.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"subtitle\",\n                  children: stat.subtitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 17\n            }, this)\n          }, stat.title, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 40,\n  columnNumber: 3\n}, this);\n_c = GameDevStats;\nexport default GameDevStats;\nvar _c;\n$RefreshReg$(_c, \"GameDevStats\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "stats", "value", "suffix", "color", "title", "subtitle", "percent", "GameDevStats", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "stat", "idx", "style", "width", "height", "cx", "cy", "r", "stroke", "strokeWidth", "fill", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "PI", "strokeDashoffset", "transition", "_c", "$RefreshReg$"], "sources": ["D:/predixo/predixo_webtech/predixo-react/src/components/game_development/GameDevStats.js"], "sourcesContent": ["import React from 'react';\r\nimport './GameDevStats.css';\r\n\r\nconst stats = [\r\n  {\r\n    value: 120,\r\n    suffix: '',\r\n    color: '#FB5343',\r\n    title: 'Games Built',\r\n    subtitle: 'Across platforms',\r\n    percent: 100,\r\n  },\r\n  {\r\n    value: 85,\r\n    suffix: '',\r\n    color: '#E33FA1',\r\n    title: 'NFT Integration',\r\n    subtitle: 'Blockchain based games',\r\n    percent: 85,\r\n  },\r\n  {\r\n    value: 75,\r\n    suffix: '',\r\n    color: '#6549D5',\r\n    title: 'Multiplayer',\r\n    subtitle: 'Online real-time games',\r\n    percent: 75,\r\n  },\r\n  {\r\n    value: 65,\r\n    suffix: '',\r\n    color: '#36C6F0',\r\n    title: 'Metaverse Ready',\r\n    subtitle: 'Immersive 3D games',\r\n    percent: 65,\r\n  },\r\n];\r\n\r\nconst GameDevStats = () => (\r\n  <section className=\"rainbow-progressbar-area rainbow-section-gap game-dev-stats-section\">\r\n    <div className=\"container\">\r\n      <div className=\"row\">\r\n        <div className=\"col-lg-12\">\r\n          <div className=\"section-title text-center\" data-sal=\"slide-up\" data-sal-duration=\"700\" data-sal-delay=\"100\">\r\n            <h4 className=\"subtitle text-center\">\r\n              <span className=\"theme-gradient\">Gaming Development Stats</span>\r\n            </h4>\r\n            <h2 className=\"title w-600 mb--20\">Our Game Development Progress</h2>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"row row--30\">\r\n        <div className=\"col-lg-10 offset-lg-1\">\r\n          <div className=\"row mt_dec--30\">\r\n            {stats.map((stat, idx) => (\r\n              <div className=\"col-lg-3 col-md-6 col-12 col-sm-6 mt--30\" key={stat.title}>\r\n                <div className=\"radial-progress-single\">\r\n                  <div className=\"radial-progress\" style={{\r\n                    '--percent': stat.percent,\r\n                    '--bar-color': stat.color,\r\n                    '--track-color': '#0f0f11',\r\n                  }}>\r\n                    <div className=\"circle-text\">\r\n                      <span className=\"count\">{stat.value}{stat.suffix}</span>\r\n                    </div>\r\n                    <svg className=\"progress-ring\" width=\"225\" height=\"225\">\r\n                      <circle className=\"progress-ring__track\" cx=\"112.5\" cy=\"112.5\" r=\"100\" stroke=\"#0f0f11\" strokeWidth=\"10\" fill=\"none\" />\r\n                      <circle className=\"progress-ring__bar\" cx=\"112.5\" cy=\"112.5\" r=\"100\" stroke={stat.color} strokeWidth=\"10\" fill=\"none\"\r\n                        strokeDasharray={2 * Math.PI * 100}\r\n                        strokeDashoffset={2 * Math.PI * 100 * (1 - stat.percent / 100)}\r\n                        style={{ transition: 'stroke-dashoffset 1.2s cubic-bezier(0.4,0,0.2,1)' }}\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"circle-info\">\r\n                    <h4 className=\"title\">{stat.title}</h4>\r\n                    <h6 className=\"subtitle\">{stat.subtitle}</h6>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n);\r\n\r\nexport default GameDevStats;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,KAAK,GAAG,CACZ;EACEC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,kBAAkB;EAC5BC,OAAO,EAAE;AACX,CAAC,EACD;EACEL,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,iBAAiB;EACxBC,QAAQ,EAAE,wBAAwB;EAClCC,OAAO,EAAE;AACX,CAAC,EACD;EACEL,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,wBAAwB;EAClCC,OAAO,EAAE;AACX,CAAC,EACD;EACEL,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,iBAAiB;EACxBC,QAAQ,EAAE,oBAAoB;EAC9BC,OAAO,EAAE;AACX,CAAC,CACF;AAED,MAAMC,YAAY,GAAGA,CAAA,kBACnBR,OAAA;EAASS,SAAS,EAAC,qEAAqE;EAAAC,QAAA,eACtFV,OAAA;IAAKS,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBV,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClBV,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBV,OAAA;UAAKS,SAAS,EAAC,2BAA2B;UAAC,YAAS,UAAU;UAAC,qBAAkB,KAAK;UAAC,kBAAe,KAAK;UAAAC,QAAA,gBACzGV,OAAA;YAAIS,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAClCV,OAAA;cAAMS,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACLd,OAAA;YAAIS,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNd,OAAA;MAAKS,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BV,OAAA;QAAKS,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpCV,OAAA;UAAKS,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BT,KAAK,CAACc,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBACnBjB,OAAA;YAAKS,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDV,OAAA;cAAKS,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCV,OAAA;gBAAKS,SAAS,EAAC,iBAAiB;gBAACS,KAAK,EAAE;kBACtC,WAAW,EAAEF,IAAI,CAACT,OAAO;kBACzB,aAAa,EAAES,IAAI,CAACZ,KAAK;kBACzB,eAAe,EAAE;gBACnB,CAAE;gBAAAM,QAAA,gBACAV,OAAA;kBAAKS,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC1BV,OAAA;oBAAMS,SAAS,EAAC,OAAO;oBAAAC,QAAA,GAAEM,IAAI,CAACd,KAAK,EAAEc,IAAI,CAACb,MAAM;kBAAA;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNd,OAAA;kBAAKS,SAAS,EAAC,eAAe;kBAACU,KAAK,EAAC,KAAK;kBAACC,MAAM,EAAC,KAAK;kBAAAV,QAAA,gBACrDV,OAAA;oBAAQS,SAAS,EAAC,sBAAsB;oBAACY,EAAE,EAAC,OAAO;oBAACC,EAAE,EAAC,OAAO;oBAACC,CAAC,EAAC,KAAK;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,IAAI;oBAACC,IAAI,EAAC;kBAAM;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvHd,OAAA;oBAAQS,SAAS,EAAC,oBAAoB;oBAACY,EAAE,EAAC,OAAO;oBAACC,EAAE,EAAC,OAAO;oBAACC,CAAC,EAAC,KAAK;oBAACC,MAAM,EAAER,IAAI,CAACZ,KAAM;oBAACqB,WAAW,EAAC,IAAI;oBAACC,IAAI,EAAC,MAAM;oBACnHC,eAAe,EAAE,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAI;oBACnCC,gBAAgB,EAAE,CAAC,GAAGF,IAAI,CAACC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAGb,IAAI,CAACT,OAAO,GAAG,GAAG,CAAE;oBAC/DW,KAAK,EAAE;sBAAEa,UAAU,EAAE;oBAAmD;kBAAE;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNd,OAAA;gBAAKS,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BV,OAAA;kBAAIS,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAEM,IAAI,CAACX;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvCd,OAAA;kBAAIS,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAEM,IAAI,CAACV;gBAAQ;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAvBuDE,IAAI,CAACX,KAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBpE,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACC,CACV;AAACkB,EAAA,GAhDIxB,YAAY;AAkDlB,eAAeA,YAAY;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}