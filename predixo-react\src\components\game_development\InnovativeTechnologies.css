.innovative-tech-section {
  padding: 80px 0 60px 0;
}
/* .section-title .subtitle .theme-gradient {
  background: linear-gradient(90deg, #ff6a00 0%, #ee0979 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
} */
.section-title .title {
  font-size: 2.1rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 18px;
}
/* .section-title .title .theme-gradient {
  background: linear-gradient(90deg, #ff6a00 0%, #ee0979 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
} */
.service__style--1.bg-color-blackest {
  background: #101223;
  border-radius: 18px;
  box-shadow: 0 2px 24px 0 rgba(0,0,0,0.08);
  margin-bottom: 2rem;
  padding: 2rem 1.5rem 1.5rem 1.5rem;
}
.service__style--1 .icon img {
  max-width: 48px;
  margin-bottom: 0.5rem;
}
.service__style--1 .title {
  font-size: 1.15rem;
  font-weight: 600;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
}
.service__style--1 .description {
  color: #bfc9e0;
  font-size: 1.01rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
@media (max-width: 991px) {
  .innovative-tech-section {
    padding: 40px 0 30px 0;
  }
  .service__style--1 {
    padding: 1.2rem 0.7rem 1rem 0.7rem;
  }
  .service__style--1 .icon img {
    max-width: 36px;
  }
}
