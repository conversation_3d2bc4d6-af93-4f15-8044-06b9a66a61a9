import React from 'react';
import './GameDevStats.css';

const stats = [
  {
    value: 120,
    suffix: '%',
    color: '#FB5343',
    title: 'Games Built',
    subtitle: 'Across platforms',
    percent: 100,
  },
  {
    value: 85,
    suffix: '%',
    color: '#E33FA1',
    title: 'NFT Integration',
    subtitle: 'Blockchain based games',
    percent: 85,
  },
  {
    value: 75,
    suffix: '%',
    color: '#6549D5',
    title: 'Multiplayer',
    subtitle: 'Online real-time games',
    percent: 75,
  },
  {
    value: 65,
    suffix: '%',
    color: '#36C6F0',
    title: 'Metaverse Ready',
    subtitle: 'Immersive 3D games',
    percent: 65,
  },
];

const GameDevStats = () => (
  <section className="rainbow-progressbar-area rainbow-section-gap game-dev-stats-section">
    <div className="container">
      <div className="row">
        <div className="col-lg-12">
          <div className="section-title text-center" data-sal="slide-up" data-sal-duration="700" data-sal-delay="100">
            <h4 className="subtitle text-center">
              <span className="theme-gradient">Gaming Development Stats</span>
            </h4>
            <h2 className="title w-600 mb--20">Our Game Development Progress</h2>
          </div>
        </div>
      </div>
      <div className="row row--30">
        <div className="col-lg-10 offset-lg-1">
          <div className="row mt_dec--30">
            {stats.map((stat, idx) => (
              <div className="col-lg-3 col-md-6 col-12 col-sm-6 mt--30" key={stat.title}>
                <div className="radial-progress-single">
                  <div className="radial-progress" style={{
                    '--percent': stat.percent,
                    '--bar-color': stat.color,
                    '--track-color': '#0f0f11',
                  }}>
                    <div className="circle-text">
                      <span className="count">{stat.value}{stat.suffix}</span>
                    </div>
                    <svg className="progress-ring" width="225" height="225">
                      <circle className="progress-ring__track" cx="112.5" cy="112.5" r="100" stroke="#0f0f11" strokeWidth="10" fill="none" />
                      <circle className="progress-ring__bar" cx="112.5" cy="112.5" r="100" stroke={stat.color} strokeWidth="10" fill="none"
                        strokeDasharray={2 * Math.PI * 100}
                        strokeDashoffset={2 * Math.PI * 100 * (1 - stat.percent / 100)}
                        style={{ transition: 'stroke-dashoffset 1.2s cubic-bezier(0.4,0,0.2,1)' }}
                      />
                    </svg>
                  </div>
                  <div className="circle-info">
                    <h4 className="title">{stat.title}</h4>
                    <h6 className="subtitle">{stat.subtitle}</h6>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default GameDevStats;
