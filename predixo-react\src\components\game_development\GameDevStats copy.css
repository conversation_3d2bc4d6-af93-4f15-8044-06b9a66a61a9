.game-dev-stats-section {
    padding: 80px 0 60px 0;
}

/* .section-title .subtitle .theme-gradient {
    background: linear-gradient(90deg, #ff6a00 0%, #ee0979 100%);
    -webkit-background-clip: text;
} */

.game-dev-stats-section {
    padding: 80px 0 60px 0;
}

/* .section-title .subtitle .theme-gradient {
    background: linear-gradient(90deg, #ff6a00 0%, #ee0979 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
} */

.section-title .title {
    font-size: 2.1rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 18px;
}

/* .section-title .title .theme-gradient {
    background: linear-gradient(90deg, #ff6a00 0%, #ee0979 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
} */

.row--30 {
    margin-left: -15px;
    margin-right: -15px;
}

.mt_dec--30 {
    margin-top: -30px;
}

.mt--30 {
    margin-top: 30px !important;
}

.radial-progress-single {
    background: transparent;
    border-radius: 18px;
    padding: 2.2rem 1.5rem 1.5rem 1.5rem;
    box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.radial-progress {
    position: relative;
    width: 120px;
    height: 120px;
    margin-bottom: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
}

.progress-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 120px;
    height: 120px;
    z-index: 1;
}

.progress-ring__track {
    stroke: #0f0f11;
    opacity: 0.25;
}

.progress-ring__bar {
    stroke-linecap: round;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
    transition: stroke-dashoffset 1.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.circle-text {
    position: relative;
    z-index: 2;
    font-size: 2.3rem;
    font-weight: 700;
    color: #fff;
    text-align: center;
}

.circle-info .title {
    font-size: 1.15rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 0.2rem;
    text-align: center;
}

.circle-info .subtitle {
    color: #bfc9e0;
    font-size: 1.01rem;
    text-align: center;
}

.radial-progress-single .circle-info {
    padding-top: 20px;
    text-align: center;
}
.radial-progress-single .circle-info .title {
    font-size: 20px;
    font-weight: 400;
    margin-bottom: 4px;
}
.radial-progress-single .circle-info .subtitle {
    font-style: italic;
    margin-bottom: 0;
    font-size: 14px;
    letter-spacing: 0.5px;
    color: var(--color-body, #bfc9e0);
}

@media (max-width: 991px) {
    .game-dev-stats-section {
        padding: 40px 0 30px 0;
    }

    .radial-progress-single {
        padding: 1.2rem 0.7rem 1rem 0.7rem;
    }

    .radial-progress {
        width: 90px;
        height: 90px;
    }

    .progress-ring {
        width: 90px;
        height: 90px;
    }

    .circle-text {
        font-size: 1.4rem;
    }
}